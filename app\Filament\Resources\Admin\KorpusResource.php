<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\KorpusResource\Pages;
use App\Filament\Resources\Admin\KorpusResource\RelationManagers;
use App\Models\Korpus;
use App\Services\UserInfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class KorpusResource extends Resource
{
    protected static ?string $model = Korpus::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';
    protected static ?string $navigationLabel = 'Блок';
    protected static ?string $modelLabel = 'блок';
    protected static ?string $pluralModelLabel = 'Блокууд';
    protected static ?int $navigationSort = 3;
    protected static ?string $slug = 'korpuses';
    protected static ?string $navigationGroup = 'Бүртгэл';

    // Hide from navigation since we only use RelationManagers
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make(Korpus::NAME)
                            ->label('Нэр')
                            ->required(),
                        Forms\Components\TextInput::make(Korpus::ORDER)
                            ->label('Дараалал')
                            ->numeric()
                            ->required(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Korpus::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Korpus::ORDER)->label('Дараалал')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('bair.name')->label('Байр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('orcs_count')->counts('orcs')->label('Орц тоо'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OrcsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKorpuses::route('/'),
            'create' => Pages\CreateKorpus::route('/create'),
            'edit' => Pages\EditKorpus::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();
        return parent::getEloquentQuery()->whereHas('bair.sukh', function (Builder $query) use($sukh) {
            $query->where('id', $sukh->id);
        });
    }
}
